<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 故障录波</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 故障录波页面专用样式
         * 基于项目统一的科技蓝色主题设计
         * 页面尺寸：1366×768像素，适配弹窗模式
         */
        .fault-wave-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .fault-wave-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .fault-wave-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: var(--shadow-secondary);
            position: relative;
            z-index: 100;
        }

        .fault-wave-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary-color) 25%,
                var(--accent-color) 50%,
                var(--primary-color) 75%,
                transparent 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .fault-wave-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .fault-wave-title h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .fault-wave-title i {
            font-size: 32px;
            color: var(--primary-color);
            animation: logoSpin 4s linear infinite;
        }

        .fault-wave-time {
            font-family: var(--font-mono);
            font-size: 16px;
            font-weight: 600;
            color: var(--accent-color);
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
        }

        /* 主内容区域 */
        .fault-wave-main {
            height: calc(768px - 80px);
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧故障录波列表面板 */
        .fault-list-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .fault-list-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .fault-list-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 筛选区域 */
        .filter-section {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            background: rgba(0, 212, 255, 0.05);
        }

        .filter-section h4 {
            font-size: 14px;
            color: var(--primary-color);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .filter-btn {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-secondary);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .filter-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: var(--bg-primary);
            font-weight: 600;
        }

        /* 故障录波文件列表 */
        .fault-list-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .fault-list-scroll {
            flex: 1;
            overflow-y: auto;
            padding: 10px 20px;
        }

        .fault-item {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .fault-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 100%;
            background: transparent;
            transition: all 0.3s ease;
        }

        .fault-item:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateX(5px);
        }

        .fault-item.selected {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .fault-item.selected::before {
            background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
        }

        .fault-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .fault-type {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            font-weight: 600;
        }

        .fault-type.voltage { color: var(--error-color); }
        .fault-type.current { color: var(--warning-color); }
        .fault-type.frequency { color: var(--primary-color); }
        .fault-type.protection { color: var(--success-color); }

        .fault-time {
            font-family: var(--font-mono);
            font-size: 12px;
            color: var(--text-secondary);
        }

        .fault-description {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 6px;
        }

        .fault-severity {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .fault-severity.high {
            background: rgba(255, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }

        .fault-severity.medium {
            background: rgba(255, 170, 0, 0.2);
            color: var(--warning-color);
            border: 1px solid var(--warning-color);
        }

        .fault-severity.low {
            background: rgba(0, 255, 136, 0.2);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        /* 右侧波形显示面板 */
        .wave-display-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 波形图区域 */
        .wave-chart-section {
            flex: 2;
            display: flex;
            flex-direction: column;
            border-bottom: 1px solid var(--border-color);
        }

        .wave-chart-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .wave-chart-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .wave-controls {
            display: flex;
            gap: 10px;
        }

        .wave-control-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .wave-control-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .wave-chart-container {
            flex: 1;
            padding: 10px;
            position: relative;
        }

        .wave-chart {
            width: 100%;
            height: 100%;
            min-height: 300px;
        }

        /* 故障详细信息区域 */
        .fault-detail-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 200px;
        }

        .fault-detail-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            padding: 12px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .fault-detail-header h3 {
            margin: 0;
            font-size: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .fault-detail-content {
            flex: 1;
            padding: 15px 20px;
            overflow-y: auto;
        }

        .detail-placeholder {
            text-align: center;
            color: var(--text-secondary);
            padding: 40px 20px;
        }

        .detail-placeholder i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: block;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .detail-item {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            padding: 10px;
        }

        .detail-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            font-family: var(--font-mono);
        }

        /* 底部操作按钮区域 */
        .fault-wave-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            z-index: 100;
        }

        .footer-left,
        .footer-right {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.1) 0%,
                rgba(0, 153, 204, 0.1) 100%);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .action-btn:hover {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--bg-primary);
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        }

        .action-btn.success {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .action-btn.success:hover {
            background: rgba(0, 255, 136, 0.2);
            border-color: var(--success-color);
        }

        .action-btn.warning {
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .action-btn.warning:hover {
            background: rgba(255, 170, 0, 0.2);
            border-color: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="fault-wave-container">
        <!-- 顶部标题栏 -->
        <div class="fault-wave-header" style="display: none;">
            <div class="fault-wave-title">
                <i class="fas fa-wave-square"></i>
                <h1>故障录波分析</h1>
            </div>
            <div class="fault-wave-time" id="currentTime">
                2025-01-04 15:39:21
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="fault-wave-main">
            <!-- 左侧故障录波列表面板 -->
            <div class="fault-list-panel">
                <div class="fault-list-header">
                    <h3>
                        <i class="fas fa-list"></i>
                        故障录波列表
                    </h3>
                </div>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <h4>
                        <i class="fas fa-filter"></i>
                        故障类型筛选
                    </h4>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-type="all">
                            <i class="fas fa-globe"></i>
                            全部
                        </button>
                        <button class="filter-btn" data-type="voltage">
                            <i class="fas fa-bolt"></i>
                            电压故障
                        </button>
                        <button class="filter-btn" data-type="current">
                            <i class="fas fa-flash"></i>
                            电流故障
                        </button>
                        <button class="filter-btn" data-type="frequency">
                            <i class="fas fa-wave-square"></i>
                            频率异常
                        </button>
                        <button class="filter-btn" data-type="protection">
                            <i class="fas fa-shield-alt"></i>
                            保护动作
                        </button>
                    </div>
                </div>

                <!-- 故障录波文件列表 -->
                <div class="fault-list-content">
                    <div class="fault-list-scroll" id="faultList">
                        <!-- 故障录波项目将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 右侧波形显示和详细信息面板 -->
            <div class="wave-display-panel">
                <!-- 波形图显示区域 -->
                <div class="wave-chart-section">
                    <div class="wave-chart-header">
                        <h3>
                            <i class="fas fa-chart-line"></i>
                            故障波形分析
                        </h3>
                        <div class="wave-controls">
                            <button class="wave-control-btn" onclick="resetZoom()">
                                <i class="fas fa-search-minus"></i>
                                重置缩放
                            </button>
                            <button class="wave-control-btn" onclick="exportWave()">
                                <i class="fas fa-download"></i>
                                导出波形
                            </button>
                        </div>
                    </div>
                    <div class="wave-chart-container">
                        <div id="waveChart" class="wave-chart"></div>
                    </div>
                </div>

                <!-- 故障详细信息面板 -->
                <div class="fault-detail-section">
                    <div class="fault-detail-header">
                        <h3>
                            <i class="fas fa-info-circle"></i>
                            故障详细信息
                        </h3>
                    </div>
                    <div class="fault-detail-content" id="faultDetailContent">
                        <div class="detail-placeholder">
                            <i class="fas fa-mouse-pointer"></i>
                            <p>请从左侧列表选择故障录波文件查看详细信息</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作按钮区域 -->
        <div class="fault-wave-footer">
            <div class="footer-left">
                <button class="action-btn primary" onclick="refreshFaultList()">
                    <i class="fas fa-sync-alt"></i>
                    刷新列表
                </button>
                <button class="action-btn" onclick="clearSelection()">
                    <i class="fas fa-times"></i>
                    清除选择
                </button>
            </div>
            <div class="footer-right">
                <button class="action-btn success" onclick="exportReport()">
                    <i class="fas fa-file-export"></i>
                    导出报告
                </button>
                <button class="action-btn warning" onclick="analyzeAll()">
                    <i class="fas fa-cogs"></i>
                    批量分析
                </button>
            </div>
        </div>
    </div>

    <script>
        /**
         * 故障录波页面JavaScript功能
         * 实现故障录波数据的显示、筛选、波形绘制等功能
         */

        // 全局变量
        let waveChart = null;
        let currentFaultData = null;
        let faultRecords = [];

        /**
         * 页面初始化函数
         */
        function initializePage() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            initializeWaveChart();
            generateMockFaultData();
            renderFaultList();
            setupEventListeners();
        }

        /**
         * 更新当前时间显示
         */
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' +
                String(now.getMonth() + 1).padStart(2, '0') + '-' +
                String(now.getDate()).padStart(2, '0') + ' ' +
                String(now.getHours()).padStart(2, '0') + ':' +
                String(now.getMinutes()).padStart(2, '0') + ':' +
                String(now.getSeconds()).padStart(2, '0');
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 初始化波形图表
         */
        function initializeWaveChart() {
            const chartDom = document.getElementById('waveChart');
            waveChart = echarts.init(chartDom, 'dark');

            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: '故障波形数据',
                    left: 'center',
                    textStyle: {
                        color: '#00d4ff',
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: '#00d4ff',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                legend: {
                    data: ['A相电压', 'B相电压', 'C相电压', 'A相电流', 'B相电流', 'C相电流'],
                    top: 30,
                    textStyle: {
                        color: '#b8c5d6'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        restore: {},
                        saveAsImage: {}
                    },
                    iconStyle: {
                        borderColor: '#00d4ff'
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: [],
                    axisLine: {
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    },
                    axisLabel: {
                        color: '#b8c5d6'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    },
                    axisLabel: {
                        color: '#b8c5d6'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                series: []
            };

            waveChart.setOption(option);
        }

        /**
         * 生成模拟故障录波数据
         */
        function generateMockFaultData() {
            const faultTypes = ['voltage', 'current', 'frequency', 'protection'];
            const faultTypeNames = {
                'voltage': '电压故障',
                'current': '电流故障',
                'frequency': '频率异常',
                'protection': '保护动作'
            };
            const severities = ['high', 'medium', 'low'];
            const severityNames = {
                'high': '严重',
                'medium': '中等',
                'low': '轻微'
            };

            faultRecords = [];
            for (let i = 0; i < 15; i++) {
                const faultType = faultTypes[Math.floor(Math.random() * faultTypes.length)];
                const severity = severities[Math.floor(Math.random() * severities.length)];
                const date = new Date();
                date.setDate(date.getDate() - Math.floor(Math.random() * 30));
                date.setHours(Math.floor(Math.random() * 24));
                date.setMinutes(Math.floor(Math.random() * 60));
                date.setSeconds(Math.floor(Math.random() * 60));

                faultRecords.push({
                    id: i + 1,
                    type: faultType,
                    typeName: faultTypeNames[faultType],
                    severity: severity,
                    severityName: severityNames[severity],
                    time: date.toISOString().slice(0, 19).replace('T', ' '),
                    description: `${faultTypeNames[faultType]} - 设备${String.fromCharCode(65 + Math.floor(Math.random() * 3))}相异常`,
                    duration: Math.floor(Math.random() * 5000) + 100, // 毫秒
                    maxValue: (Math.random() * 1000 + 100).toFixed(2),
                    minValue: (Math.random() * 50 + 10).toFixed(2),
                    avgValue: (Math.random() * 500 + 50).toFixed(2)
                });
            }

            // 按时间排序，最新的在前
            faultRecords.sort((a, b) => new Date(b.time) - new Date(a.time));
        }

        /**
         * 渲染故障录波列表
         */
        function renderFaultList(filterType = 'all') {
            const faultListContainer = document.getElementById('faultList');
            const filteredRecords = filterType === 'all' ?
                faultRecords :
                faultRecords.filter(record => record.type === filterType);

            faultListContainer.innerHTML = '';

            filteredRecords.forEach(record => {
                const faultItem = document.createElement('div');
                faultItem.className = 'fault-item';
                faultItem.dataset.faultId = record.id;
                faultItem.onclick = () => selectFaultRecord(record);

                faultItem.innerHTML = `
                    <div class="fault-item-header">
                        <div class="fault-type ${record.type}">
                            <i class="fas ${getFaultTypeIcon(record.type)}"></i>
                            ${record.typeName}
                        </div>
                        <div class="fault-time">${record.time}</div>
                    </div>
                    <div class="fault-description">${record.description}</div>
                    <div class="fault-severity ${record.severity}">${record.severityName}</div>
                `;

                faultListContainer.appendChild(faultItem);
            });
        }

        /**
         * 获取故障类型对应的图标
         */
        function getFaultTypeIcon(type) {
            const icons = {
                'voltage': 'fa-bolt',
                'current': 'fa-flash',
                'frequency': 'fa-wave-square',
                'protection': 'fa-shield-alt'
            };
            return icons[type] || 'fa-exclamation-triangle';
        }

        /**
         * 选择故障录波记录
         */
        function selectFaultRecord(record) {
            // 移除之前的选中状态
            document.querySelectorAll('.fault-item.selected').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加当前选中状态
            document.querySelector(`[data-fault-id="${record.id}"]`).classList.add('selected');

            currentFaultData = record;
            displayFaultDetails(record);
            generateWaveformData(record);
        }

        /**
         * 显示故障详细信息
         */
        function displayFaultDetails(record) {
            const detailContent = document.getElementById('faultDetailContent');

            detailContent.innerHTML = `
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">故障ID</div>
                        <div class="detail-value">#${String(record.id).padStart(4, '0')}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">故障类型</div>
                        <div class="detail-value">${record.typeName}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">发生时间</div>
                        <div class="detail-value">${record.time}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">严重程度</div>
                        <div class="detail-value">${record.severityName}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">持续时间</div>
                        <div class="detail-value">${record.duration}ms</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">最大值</div>
                        <div class="detail-value">${record.maxValue}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">最小值</div>
                        <div class="detail-value">${record.minValue}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">平均值</div>
                        <div class="detail-value">${record.avgValue}</div>
                    </div>
                </div>
            `;
        }

        /**
         * 生成波形数据并更新图表
         */
        function generateWaveformData(record) {
            const timePoints = [];
            const voltageA = [];
            const voltageB = [];
            const voltageC = [];
            const currentA = [];
            const currentB = [];
            const currentC = [];

            // 生成时间点（故障前后各100个点）
            for (let i = -100; i <= 100; i++) {
                timePoints.push(i + 'ms');

                // 生成模拟波形数据
                const t = i * 0.01;
                const faultFactor = Math.abs(i) < 20 ? (1 + Math.random() * 2) : 1; // 故障时刻波形异常

                voltageA.push((220 * Math.sin(t * 2 * Math.PI) * faultFactor + Math.random() * 10).toFixed(2));
                voltageB.push((220 * Math.sin(t * 2 * Math.PI - 2 * Math.PI / 3) * faultFactor + Math.random() * 10).toFixed(2));
                voltageC.push((220 * Math.sin(t * 2 * Math.PI + 2 * Math.PI / 3) * faultFactor + Math.random() * 10).toFixed(2));

                currentA.push((10 * Math.sin(t * 2 * Math.PI + Math.PI / 6) * faultFactor + Math.random() * 2).toFixed(2));
                currentB.push((10 * Math.sin(t * 2 * Math.PI - 2 * Math.PI / 3 + Math.PI / 6) * faultFactor + Math.random() * 2).toFixed(2));
                currentC.push((10 * Math.sin(t * 2 * Math.PI + 2 * Math.PI / 3 + Math.PI / 6) * faultFactor + Math.random() * 2).toFixed(2));
            }

            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: `故障录波 #${String(record.id).padStart(4, '0')} - ${record.typeName}`,
                    left: 'center',
                    textStyle: {
                        color: '#00d4ff',
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: '#00d4ff',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                legend: {
                    data: ['A相电压', 'B相电压', 'C相电压', 'A相电流', 'B相电流', 'C相电流'],
                    top: 30,
                    textStyle: {
                        color: '#b8c5d6'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        restore: {},
                        saveAsImage: {}
                    },
                    iconStyle: {
                        borderColor: '#00d4ff'
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: timePoints,
                    axisLine: {
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    },
                    axisLabel: {
                        color: '#b8c5d6'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    },
                    axisLabel: {
                        color: '#b8c5d6'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.2)'
                        }
                    }
                },
                series: [
                    {
                        name: 'A相电压',
                        type: 'line',
                        data: voltageA,
                        smooth: true,
                        lineStyle: {
                            color: '#ff4444',
                            width: 2
                        },
                        symbol: 'none'
                    },
                    {
                        name: 'B相电压',
                        type: 'line',
                        data: voltageB,
                        smooth: true,
                        lineStyle: {
                            color: '#ffaa00',
                            width: 2
                        },
                        symbol: 'none'
                    },
                    {
                        name: 'C相电压',
                        type: 'line',
                        data: voltageC,
                        smooth: true,
                        lineStyle: {
                            color: '#00ff88',
                            width: 2
                        },
                        symbol: 'none'
                    },
                    {
                        name: 'A相电流',
                        type: 'line',
                        data: currentA,
                        smooth: true,
                        lineStyle: {
                            color: '#00d4ff',
                            width: 2
                        },
                        symbol: 'none'
                    },
                    {
                        name: 'B相电流',
                        type: 'line',
                        data: currentB,
                        smooth: true,
                        lineStyle: {
                            color: '#66e0ff',
                            width: 2
                        },
                        symbol: 'none'
                    },
                    {
                        name: 'C相电流',
                        type: 'line',
                        data: currentC,
                        smooth: true,
                        lineStyle: {
                            color: '#0099cc',
                            width: 2
                        },
                        symbol: 'none'
                    }
                ]
            };

            waveChart.setOption(option);
        }

        /**
         * 设置事件监听器
         */
        function setupEventListeners() {
            // 筛选按钮事件
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除其他按钮的active状态
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    // 添加当前按钮的active状态
                    this.classList.add('active');

                    // 根据筛选类型重新渲染列表
                    const filterType = this.dataset.type;
                    renderFaultList(filterType);
                });
            });

            // 窗口大小改变时重新调整图表
            window.addEventListener('resize', function() {
                if (waveChart) {
                    waveChart.resize();
                }
            });
        }

        /**
         * 重置波形图缩放
         */
        function resetZoom() {
            if (waveChart) {
                waveChart.dispatchAction({
                    type: 'dataZoom',
                    start: 0,
                    end: 100
                });
            }
        }

        /**
         * 导出波形图
         */
        function exportWave() {
            if (waveChart && currentFaultData) {
                const url = waveChart.getDataURL({
                    pixelRatio: 2,
                    backgroundColor: '#0a0e1a'
                });

                const link = document.createElement('a');
                link.download = `故障录波_${currentFaultData.id}_${currentFaultData.time.replace(/[:\s]/g, '_')}.png`;
                link.href = url;
                link.click();
            } else {
                alert('请先选择一个故障录波记录');
            }
        }

        /**
         * 刷新故障列表
         */
        function refreshFaultList() {
            generateMockFaultData();
            const activeFilter = document.querySelector('.filter-btn.active');
            const filterType = activeFilter ? activeFilter.dataset.type : 'all';
            renderFaultList(filterType);

            // 清除当前选择
            currentFaultData = null;
            document.getElementById('faultDetailContent').innerHTML = `
                <div class="detail-placeholder">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>请从左侧列表选择故障录波文件查看详细信息</p>
                </div>
            `;

            // 重置波形图
            initializeWaveChart();
        }

        /**
         * 清除选择
         */
        function clearSelection() {
            document.querySelectorAll('.fault-item.selected').forEach(item => {
                item.classList.remove('selected');
            });

            currentFaultData = null;
            document.getElementById('faultDetailContent').innerHTML = `
                <div class="detail-placeholder">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>请从左侧列表选择故障录波文件查看详细信息</p>
                </div>
            `;

            // 重置波形图
            initializeWaveChart();
        }

        /**
         * 导出故障报告
         */
        function exportReport() {
            if (!currentFaultData) {
                alert('请先选择一个故障录波记录');
                return;
            }

            // 生成报告内容
            const reportContent = `
故障录波分析报告
================

故障基本信息：
- 故障ID: #${String(currentFaultData.id).padStart(4, '0')}
- 故障类型: ${currentFaultData.typeName}
- 发生时间: ${currentFaultData.time}
- 严重程度: ${currentFaultData.severityName}
- 故障描述: ${currentFaultData.description}

故障参数：
- 持续时间: ${currentFaultData.duration}ms
- 最大值: ${currentFaultData.maxValue}
- 最小值: ${currentFaultData.minValue}
- 平均值: ${currentFaultData.avgValue}

分析结论：
根据故障录波数据分析，该故障为${currentFaultData.typeName}，
严重程度为${currentFaultData.severityName}，建议及时处理。

报告生成时间: ${new Date().toLocaleString()}
            `;

            // 创建并下载文件
            const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = `故障报告_${currentFaultData.id}_${currentFaultData.time.replace(/[:\s]/g, '_')}.txt`;
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);
        }

        /**
         * 批量分析故障
         */
        function analyzeAll() {
            if (faultRecords.length === 0) {
                alert('没有故障录波数据可供分析');
                return;
            }

            // 统计分析
            const typeStats = {};
            const severityStats = {};

            faultRecords.forEach(record => {
                typeStats[record.typeName] = (typeStats[record.typeName] || 0) + 1;
                severityStats[record.severityName] = (severityStats[record.severityName] || 0) + 1;
            });

            let analysisResult = '批量分析结果：\n\n';
            analysisResult += `总故障数量: ${faultRecords.length}\n\n`;

            analysisResult += '故障类型分布：\n';
            Object.entries(typeStats).forEach(([type, count]) => {
                analysisResult += `- ${type}: ${count}次 (${(count/faultRecords.length*100).toFixed(1)}%)\n`;
            });

            analysisResult += '\n严重程度分布：\n';
            Object.entries(severityStats).forEach(([severity, count]) => {
                analysisResult += `- ${severity}: ${count}次 (${(count/faultRecords.length*100).toFixed(1)}%)\n`;
            });

            alert(analysisResult);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
