# 桂林智源 SVG 数字化系统 - 历史事件页面说明

## 页面概述

**历史事件.html** 是一个专门用于历史事件查询和管理的独立页面，完全符合桂林智源 SVG 数字化系统的设计风格和技术规范。

## 页面规格

- **页面尺寸**: 1366×768像素（标准化弹窗尺寸）
- **设计风格**: 科技感深色主题，与主系统界面保持一致
- **响应式设计**: 适配大屏显示，无滚动条设计
- **技术栈**: HTML5 + CSS3 + JavaScript

## 功能特性

### 1. 事件类型分类
- **告警事件**: 黄色标识，表示系统告警信息
- **故障事件**: 红色标识，表示系统故障信息
- **恢复事件**: 白色标识，表示系统恢复信息

### 2. 时间格式规范
- **日期格式**: YYYY-MM-DD
- **时间格式**: HH:mm:ss
- **分列显示**: 日期和时间分别显示在不同列中
- **实时生成**: 支持实时时间戳生成用于演示

### 3. 序号管理
- **右对齐**: 序号列右对齐显示
- **固定宽度**: 40-50px宽度设计
- **自动递增**: 按时间顺序自动递增编号
- **最新优先**: 最新事件获得最高序号

### 4. 筛选功能
- **事件类型筛选**: 支持按告警、故障、恢复事件筛选
- **设备类型筛选**: 支持按SVG系统、水冷系统、通信模块筛选
- **时间范围筛选**: 支持自定义开始和结束时间
- **实时统计**: 显示各类型事件的实时统计数量

### 5. 界面布局
- **左侧筛选面板**: 300px宽度，包含所有筛选条件
- **右侧事件列表**: 自适应宽度，显示事件详细信息
- **表格式布局**: 采用网格布局，列宽固定便于阅读
- **悬停效果**: 鼠标悬停时显示高亮效果

## 数据结构

### 事件数据格式
```javascript
{
    id: 1,                          // 事件唯一标识
    type: 'alarm',                  // 事件类型：alarm/fault/recovery
    device: 'SVG主控',              // 设备名称
    deviceCategory: 'svg',          // 设备分类
    message: '设备温度略高，已自动调节', // 事件描述
    timestamp: 1704067200000,       // 时间戳
    date: '2025-01-01',            // 格式化日期
    time: '00:00:00',              // 格式化时间
    serial: 1                       // 序号
}
```

### 筛选条件格式
```javascript
{
    type: 'all',        // 事件类型筛选
    device: 'all',      // 设备类型筛选
    startDate: null,    // 开始时间
    endDate: null       // 结束时间
}
```

## 技术实现

### 核心函数
- `initHistoryPage()`: 初始化页面
- `generateHistoryEvents()`: 生成模拟历史事件数据
- `applyFilters()`: 应用筛选条件
- `renderEventsList()`: 渲染事件列表
- `updateStatistics()`: 更新统计信息
- `showEventDetails()`: 显示事件详情

### CSS类名映射
- `.event-item.alarm`: 告警事件样式（黄色）
- `.event-item.fault`: 故障事件样式（红色）
- `.event-item.recovery`: 恢复事件样式（白色）
- `.filter-btn.active`: 激活状态的筛选按钮

### 颜色编码规范
- **告警事件**: `var(--warning-color)` - 黄色 (#ffaa00)
- **故障事件**: `var(--error-color)` - 红色 (#ff4444)
- **恢复事件**: `var(--text-primary)` - 白色 (#ffffff)

## 使用说明

### 1. 页面访问
- **直接访问**: 在浏览器中打开 `历史事件.html`
- **主页面调用**: 通过主页面菜单的"历史事件"选项访问
- **iframe嵌入**: 支持在1366×768像素的iframe中嵌入

### 2. 筛选操作
1. **事件类型筛选**: 点击左侧"事件类型"区域的按钮
2. **设备类型筛选**: 点击左侧"设备类型"区域的按钮
3. **时间范围筛选**: 设置开始和结束时间，点击"应用筛选"
4. **重置筛选**: 选择"所有事件"和"所有设备"重置筛选条件

### 3. 事件查看
- **列表浏览**: 在右侧事件列表中浏览所有事件
- **详情查看**: 点击任意事件行查看详细信息
- **统计信息**: 查看顶部统计栏了解事件分布情况

## 数据模拟

### 模拟数据特点
- **数据量**: 生成150条历史事件记录
- **时间范围**: 覆盖最近30天
- **事件分布**: 随机分布各类型事件
- **设备覆盖**: 包含SVG系统、水冷系统、通信模块等11种设备

### 事件消息模板
- **告警消息**: 8种不同的告警描述模板
- **故障消息**: 8种不同的故障描述模板
- **恢复消息**: 8种不同的恢复描述模板

## 扩展功能

### 可扩展特性
1. **数据导出**: 可扩展为支持CSV/Excel导出功能
2. **事件详情**: 可扩展为显示更详细的事件信息弹窗
3. **实时更新**: 可扩展为支持实时事件推送
4. **高级筛选**: 可扩展为支持更多筛选条件
5. **数据分析**: 可扩展为支持事件趋势分析图表

### 集成说明
- **主页面集成**: 已集成到主页面的下拉菜单中
- **弹窗调用**: 支持通过iframe在1366×768像素弹窗中显示
- **样式继承**: 完全继承主系统的CSS变量和样式规范
- **字体图标**: 使用Font Awesome 6.0.0图标库

## 维护说明

### 文件位置
- **HTML文件**: `webgl/历史事件.html`
- **样式依赖**: `webgl/styles.css`
- **图标依赖**: Font Awesome CDN
- **主页面集成**: `webgl/main.html`

### 更新注意事项
1. 保持与主系统样式的一致性
2. 确保1366×768像素尺寸规范
3. 维护颜色编码的统一性
4. 保持时间格式的规范性

---

**文档版本**: v1.0  
**创建日期**: 2025-01-04  
**维护人员**: 桂林智源技术团队
