/**
 * Unity WebGL 模板样式文件
 * 优化后的紧凑版本
 */

/* 基础样式 */
body {
    padding: 0;
    margin: 0;
}

/* Unity 容器 */
#unity-container {
    position: fixed;
    width: 100%;
    height: 100%;
}

#unity-canvas {
    width: 100%;
    height: 100%;
    background: #231F20;
}

/* 加载界面 */
#unity-loading-bar {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

#unity-logo {
    width: 154px;
    height: 130px;
    background: url('unity-logo-dark.png') no-repeat center;
}

/* 进度条 */
#unity-progress-bar-empty,
#unity-progress-bar-full {
    width: 141px;
    height: 18px;
    margin-top: 10px;
    background-repeat: no-repeat;
    background-position: center;
}

#unity-progress-bar-empty {
    margin-left: auto;
    margin-right: auto;
    background-image: url('progress-bar-empty-dark.png');
}

#unity-progress-bar-full {
    width: 0%;
    background-image: url('progress-bar-full-dark.png');
}

/* 警告信息 */
#unity-warning {
    position: absolute;
    left: 50%;
    top: 5%;
    transform: translate(-50%);
    background: white;
    padding: 10px;
    display: none;
}
