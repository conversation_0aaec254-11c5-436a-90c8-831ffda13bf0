# SVG数字化系统弹窗功能实现说明

## 功能概述

已成功实现了三个弹窗功能：

1. **I/O状态弹窗** - 显示输入输出端口状态信息
2. **电气拓扑弹窗** - 显示电气系统拓扑图
3. **水冷拓扑弹窗** - 显示水冷系统拓扑图

## 实现的功能特性

### 1. I/O状态弹窗
- **触发方式**: 点击右侧面板"相关链接"区域的"I/O状态"按钮
- **显示内容**: 
  - 左侧：输入IO状态（32个输入端口）
  - 右侧：输出IO状态（32个输出端口）
- **状态指示**:
  - 输入端口：圆形指示器，绿色表示激活，灰色表示非激活
  - 输出端口：方形指示器，绿色表示激活，灰色表示非激活
- **实时更新**: 模拟I/O状态的实时变化
- **动画效果**: 激活状态的指示器有脉冲动画效果

### 2. 电气拓扑弹窗
- **触发方式**: 点击右侧面板"相关链接"区域的"电气拓扑"按钮
- **显示内容**: 显示SVG系统拓扑图（../docs/SVG系统拓扑图.png）
- **交互功能**: 图片可以悬停放大

### 3. 水冷拓扑弹窗
- **触发方式**: 点击右侧面板"相关链接"区域的"水冷拓扑"按钮
- **显示内容**: 显示水冷系统拓扑图（../docs/水冷系统拓扑图.png）
- **交互功能**: 图片可以悬停放大

## 通用弹窗特性

### 交互方式
- **打开**: 点击对应按钮
- **关闭**: 
  - 点击弹窗右上角的关闭按钮（×）
  - 点击弹窗外部的遮罩区域
  - 按ESC键

### 视觉效果
- **背景遮罩**: 半透明黑色背景，带模糊效果
- **动画效果**: 弹窗出现和消失都有缩放动画
- **响应式设计**: 适配不同屏幕尺寸

### 样式特点
- **科技感设计**: 蓝色主题，符合整体界面风格
- **玻璃态效果**: 背景模糊和半透明效果
- **发光边框**: 使用主题色的发光边框
- **图标支持**: 使用Font Awesome图标

## 技术实现

### HTML结构
- 弹窗HTML结构添加在main.html的底部
- 使用语义化的HTML标签和类名
- 支持无障碍访问

### CSS样式
- 在styles.css末尾添加了完整的弹窗样式
- 使用CSS变量保持主题一致性
- 响应式设计支持移动端

### JavaScript功能
- 弹窗显示/隐藏控制函数
- 事件监听器处理用户交互
- I/O状态模拟更新功能
- 动画效果控制

## 文件修改清单

### 修改的文件
1. **webgl/main.html**
   - 添加了I/O状态弹窗HTML结构
   - 添加了拓扑图弹窗HTML结构
   - 修改了openIOStatus()、openCoolingTopology()、openSystemTopology()函数
   - 添加了弹窗控制JavaScript函数

2. **webgl/styles.css**
   - 添加了完整的弹窗样式定义
   - 包括I/O状态弹窗和拓扑图弹窗的专用样式

### 新增的文件
1. **webgl/test-modal.html** - 弹窗功能测试页面
2. **webgl/弹窗功能说明.md** - 本说明文档

## 使用方法

1. 打开 `webgl/main.html` 页面
2. 在右侧面板的"相关链接"区域找到以下按钮：
   - "I/O状态" - 打开I/O状态监控弹窗
   - "电气拓扑" - 打开电气系统拓扑图弹窗
   - "水冷拓扑" - 打开水冷系统拓扑图弹窗
3. 点击按钮即可打开对应的弹窗
4. 使用多种方式关闭弹窗（关闭按钮、点击外部、ESC键）

## 测试页面

可以使用 `webgl/test-modal.html` 页面单独测试弹窗功能，该页面包含了三个测试按钮，可以独立验证每个弹窗的功能。

## 注意事项

1. 确保拓扑图文件存在于正确路径（../docs/目录下）
2. 弹窗样式依赖于主CSS文件中的CSS变量
3. I/O状态更新是模拟数据，实际使用时需要连接真实的数据源
4. 弹窗在移动端会自动适配屏幕尺寸

## 扩展建议

1. 可以为I/O状态添加更多的交互功能，如点击查看详细信息
2. 可以为拓扑图添加缩放和拖拽功能
3. 可以添加更多类型的弹窗，如设备详情、历史数据等
4. 可以集成真实的数据源替换模拟数据
