# 桂林智源 SVG 数字化系统 - 版本信息页面说明

## 页面概述

**版本信息.html** 是一个专门用于显示系统版本信息的独立页面，完全符合桂林智源 SVG 数字化系统的设计风格和技术规范。

## 页面规格

- **页面尺寸**: 1366×768像素（标准化弹窗尺寸）
- **设计风格**: 科技感深色主题，与主系统界面保持一致
- **响应式设计**: 适配大屏显示，无滚动条设计
- **技术栈**: HTML5 + CSS3 + JavaScript

## 功能特性

### 1. 设备信息展示
- **主要设备版本**: 显示主控CPU板、CPLD、FPGA等核心设备的版本信息
- **版本号显示**: 清晰展示每个设备的具体版本号
- **修改日期**: 显示版本的最后修改日期
- **表格布局**: 采用清晰的三列表格布局（名称、版本、修改日期）

### 2. 三相单元版本信息
- **分相显示**: 分别显示A相、B相、C相的单元版本信息
- **并列布局**: 三个表格并列显示，便于对比查看
- **单元编号**: 每相包含12个单元的版本信息
- **版本日期**: 显示每个单元的版本日期

### 3. 界面设计特色
- **科技蓝色主题**: 采用统一的#00d4ff主色调
- **渐变背景**: 深色科技感背景，带有动态光效
- **表格样式**: 专业的工业监控界面表格设计
- **悬停效果**: 鼠标悬停时的高亮显示效果
- **动画效果**: 页面加载时的渐入动画

## 技术实现

### 前端技术栈
- **HTML5**: 语义化标签和现代HTML结构
- **CSS3**: 现代样式设计，支持渐变、动画、响应式
- **JavaScript ES6+**: 数据处理和DOM操作
- **Font Awesome 6.0**: 图标库，提供丰富的界面图标

### 样式特性
- **CSS变量系统**: 继承项目统一色彩方案
- **Grid布局**: 使用CSS Grid实现三列并列布局
- **Flexbox布局**: 灵活的垂直布局管理
- **动画效果**: 渐入动画和悬停过渡效果

### 数据结构
```javascript
// 设备信息数据结构
const deviceInfo = [
    { 
        name: '主控CPU板DSP版本号', 
        version: 'V2.1.3', 
        date: '2024-12-15' 
    }
];

// 相单元版本数据结构
const phaseVersions = [
    { 
        name: 'A相第1单元版本', 
        date: '2024-12-01' 
    }
];
```

## 页面布局

### 上半部分 - 设备信息表格
- **标题区域**: 带图标的"设备信息"标题
- **表格内容**: 主要设备的版本信息
- **列宽设计**: 名称列较宽，版本和日期列适中

### 下半部分 - 三相单元版本
- **标题区域**: 带图标的"单元版本信息"标题
- **三列布局**: A相、B相、C相并列显示
- **表格结构**: 每个相包含名称和版本日期两列

## 集成方式

### 主系统集成
- **文件位置**: `webgl/版本信息.html`
- **弹窗调用**: 通过main.html的导航菜单访问
- **iframe嵌入**: 支持1366×768像素弹窗模式
- **样式继承**: 完全兼容项目styles.css样式系统

### 导航菜单配置
已在main.html中配置版本信息菜单项：
```javascript
case 'version-info':
    showModuleModal('version-info', '版本信息', 'fas fa-info-circle', '版本信息.html');
    break;
```

## 数据管理

### 版本信息维护
- **设备信息**: 可通过修改deviceInfo数组更新设备版本
- **单元版本**: 可通过修改phaseAVersions、phaseBVersions、phaseCVersions数组更新
- **日期格式**: 统一使用YYYY-MM-DD格式
- **版本号格式**: 建议使用Vx.x.x格式

### 扩展性设计
- **数据源**: 可轻松替换为从API获取的实时数据
- **表格结构**: 可根据需要调整列数和内容
- **样式定制**: 可通过CSS变量调整颜色和样式

## 使用说明

### 基本操作
1. 通过主界面菜单选择"版本信息"
2. 页面自动加载并显示所有版本信息
3. 可查看设备信息和三相单元版本详情

### 信息查看
1. **设备信息**: 查看主要设备的版本号和修改日期
2. **单元版本**: 对比查看A、B、C三相的单元版本信息
3. **悬停效果**: 鼠标悬停在表格行上可高亮显示

## 维护建议

### 定期更新
- 建议定期更新设备版本信息
- 保持版本日期的准确性
- 及时反映系统升级情况

### 数据一致性
- 确保版本信息与实际系统一致
- 统一版本号命名规范
- 保持日期格式的统一性

## 兼容性

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 分辨率适配
- 主要针对1366×768像素设计
- 支持1080p及以上分辨率
- 响应式设计适配不同屏幕尺寸

## 文件结构

```
webgl/
├── 版本信息.html          # 版本信息页面主文件
├── styles.css             # 项目统一样式文件
├── main.html              # 主页面（包含菜单配置）
└── 版本信息页面说明.md     # 本说明文档
```

## 更新日志

### v1.0.0 (2025-01-04)
- ✨ 初始版本发布
- 🎨 实现科技蓝色主题设计
- 📊 完成设备信息和三相单元版本展示
- 🔄 集成到主系统菜单
- 📱 实现响应式布局设计
- ⚡ 优化页面加载和动画效果
