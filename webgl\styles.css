/**
 * 白云电气设备数字孪生系统 - 高保真样式文件
 * 基于科技蓝色主题的现代化设计
 *
 * 优化说明：
 * - 合并了重复的CSS规则和选择器
 * - 删除了冗余的样式定义
 * - 统一了参数、状态、拓扑等组件的样式
 * - 保持了完整的视觉效果和功能
 * - 优化了代码结构和可读性
 */

/* 全局样式和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 确保列表样式重置 */
ul, ol, li {
    list-style: none;
    margin: 0;
    padding: 0;
}

:root {
    /* 科技蓝色主题色彩 */
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #66e0ff;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --error-color: #ff4444;

    /* 背景色 */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #2a3142;
    --bg-card: rgba(26, 31, 46, 0.8);
    --bg-glass: rgba(0, 212, 255, 0.1);

    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #7a8ba0;

    /* 边框和阴影 */
    --border-color: rgba(0, 212, 255, 0.3);
    --shadow-primary: 0 4px 20px rgba(0, 212, 255, 0.2);
    --shadow-secondary: 0 2px 10px rgba(0, 0, 0, 0.3);

    /* 字体 */
    --font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    --font-mono: 'Consolas', 'Monaco', monospace;

    /* 弹窗尺寸 */
    /* 弹窗尺寸现在由内容自适应，不再需要固定变量 */
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
    position: relative;
}

/* 科技感背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 主应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    animation: fadeInUp 0.8s ease-out;
}

/* 顶部导航栏 */
.header {
    height: 70px;
    background: linear-gradient(90deg,
        rgba(26, 31, 46, 0.95) 0%,
        rgba(42, 49, 66, 0.95) 50%,
        rgba(26, 31, 46, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: var(--shadow-secondary);
    position: relative;
    z-index: 100;
    animation: fadeInUp 0.6s ease-out 0.1s both;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-color) 25%,
        var(--accent-color) 50%,
        var(--primary-color) 75%,
        transparent 100%);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.logo-image {
    width: 32px;
    height: 32px;
    object-fit: contain;
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.3));
}

.logo i {
    font-size: 28px;
    animation: logoSpin 4s linear infinite;
}

.logo-text {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: neonGlow 3s ease-in-out infinite alternate;
}

.system-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    letter-spacing: 1px;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.system-info-panel {
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 280px;
    justify-content: flex-end;
}

.time-display {
    font-family: var(--font-mono);
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-color);
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.15) 0%,
        rgba(0, 153, 204, 0.15) 100%);
    padding: 8px 16px;
    border-radius: 18px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    box-shadow:
        0 2px 8px rgba(0, 212, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    letter-spacing: 0.5px;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.time-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.2) 50%,
        transparent 100%);
    animation: timeShimmer 3s ease-in-out infinite;
}

@keyframes timeShimmer {
    0%, 100% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
    background: linear-gradient(135deg,
        rgba(0, 255, 136, 0.12) 0%,
        rgba(0, 255, 136, 0.08) 100%);
    padding: 6px 12px;
    border-radius: 14px;
    border: 1px solid rgba(0, 255, 136, 0.3);
    backdrop-filter: blur(5px);
    box-shadow: 0 1px 4px rgba(0, 255, 136, 0.2);
}

.connection-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.connection-status .status-indicator.online {
    background: var(--success-color);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
}

.connection-status .status-indicator.online i {
    font-size: 6px;
    color: var(--bg-primary);
    animation: pulse 2s ease-in-out infinite;
}

.connection-status span {
    color: var(--success-color);
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 220px;
}

.status-indicators {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.status-item.online {
    color: var(--success-color);
}

.status-item i {
    font-size: 12px;
}

.status-item.online i {
    animation: pulse 2s ease-in-out infinite;
}

.controls {
    display: flex;
    gap: 15px;
}

.control-btn {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(0, 153, 204, 0.1) 100%);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.2) 50%,
        transparent 100%);
    transition: left 0.5s ease;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:hover {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.2) 0%,
        rgba(0, 153, 204, 0.2) 100%);
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

.control-btn.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--bg-primary);
    font-weight: 600;
}

.control-btn.primary:hover {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.5);
}

.control-btn i {
    font-size: 16px;
}

.control-btn:active {
    transform: scale(0.98);
}

/* 主内容区域 - 左中右三栏布局 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 3fr 1fr;
    gap: 16px;
    padding: 16px;
    overflow: hidden;
    height: calc(100vh - 140px); /* 减去header和footer的高度，为1080p优化 */
}

/* 左侧面板 - 系统状态与参数 */
.left-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-primary);
    overflow-y: auto; /* 改为垂直滚动 */
    display: flex;
    flex-direction: column;
    animation: fadeInLeft 0.8s ease-out 0.2s both;
    height: 100%; /* 使用100%高度，由grid容器控制 */
}

/* 系统状态区域 - 自适应高度 */
.system-status-area {
    flex: none; /* 改为自适应大小 */
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--border-color);
    overflow: visible; /* 允许内容显示 */
    min-height: auto; /* 自适应最小高度 */
}

/* 水冷系统区域 - 自适应高度 */
.cooling-system-area {
    flex: none; /* 改为自适应大小 */
    display: flex;
    flex-direction: column;
    overflow: visible; /* 允许内容显示 */
    min-height: auto; /* 自适应最小高度 */
}

/* 中央3D展示区域 */
.center-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-primary);
    position: relative;
    overflow: hidden;
    animation: scaleIn 0.8s ease-out 0.3s both;
    display: flex;
    flex-direction: column;
}

.unity-container {
    width: 100%;
    flex: 1;
    position: relative;
    min-height: 400px;
}

.unity-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 12px;
}

.unity-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.05) 0%,
        rgba(0, 153, 204, 0.05) 100%);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}

.placeholder-content {
    text-align: center;
    color: var(--text-secondary);
}

.placeholder-content i {
    font-size: 64px;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: block;
}

.placeholder-content h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.placeholder-content p {
    font-size: 16px;
    margin-bottom: 30px;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(0, 212, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
    width: 0%;
    animation: loadingProgress 3s ease-in-out infinite;
}

/* 扫描线效果 */
.unity-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%);
    animation: scanLine 4s linear infinite;
    z-index: 10;
    opacity: 0.6;
    transform: translateY(-100%);
}

/* 粒子效果模拟 */
.unity-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, var(--primary-color), transparent),
        radial-gradient(2px 2px at 40px 70px, var(--accent-color), transparent),
        radial-gradient(1px 1px at 90px 40px, var(--primary-color), transparent),
        radial-gradient(1px 1px at 130px 80px, var(--accent-color), transparent),
        radial-gradient(2px 2px at 160px 30px, var(--primary-color), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: dataFlow 20s linear infinite;
    opacity: 0.1;
    pointer-events: none;
}

/* 3D场景工具栏 */
.scene-toolbar {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    gap: 5px;
    z-index: 20;
}

.toolbar-group {
    display: flex;
    gap: 5px;
}

.toolbar-btn {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.toolbar-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

.toolbar-btn:active {
    transform: scale(0.98);
}

/* 右侧数据面板 */
.right-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-primary);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    animation: fadeInRight 0.8s ease-out 0.4s both;
    height: 100%; /* 使用100%高度，由grid容器控制 */
}

.panel-header {
    padding: 15px 18px;
    border-bottom: 2px solid var(--border-color);
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.15) 0%,
        rgba(0, 153, 204, 0.08) 100%);
    flex-shrink: 0;
    position: relative;
}

.panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--accent-color) 50%,
        var(--primary-color) 100%);
}

.panel-header h3 {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    letter-spacing: 0.8px;
    text-transform: uppercase;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.panel-header h3 i {
    font-size: 22px;
    color: var(--primary-color);
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
}

/* 关键指标卡片 */
.metrics-cards {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metric-card {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.08) 0%,
        rgba(0, 153, 204, 0.08) 100%);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.metric-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%);
    animation: dataFlow 3s ease-in-out infinite;
}

.metric-card:hover {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.15) 0%,
        rgba(0, 153, 204, 0.15) 100%);
    border-color: var(--primary-color);
    transform: translateY(-2px) rotateX(5deg);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.metric-card:hover::before {
    opacity: 1;
}

.metric-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-primary);
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    font-family: var(--font-mono);
    margin-bottom: 4px;
}

.metric-label {
    font-size: 14px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-trend {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.metric-trend.up {
    background: rgba(0, 255, 136, 0.2);
    color: var(--success-color);
}

.metric-trend.down {
    background: rgba(255, 68, 68, 0.2);
    color: var(--error-color);
}

.metric-trend.stable {
    background: rgba(184, 197, 214, 0.2);
    color: var(--text-secondary);
}

/* 图表区域 */
.charts-section {
    flex: 1;
    padding: 0 20px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-container {
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    flex: 1;
    min-height: 200px;
    position: relative;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%);
    animation: dataFlow 2s ease-in-out infinite;
}

.chart-header {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(0, 153, 204, 0.1) 100%);
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h4 {
    font-size: 16px;
    color: var(--text-primary);
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    position: relative;
    overflow: hidden;
}

.chart-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--bg-primary);
}

.chart-btn:active {
    transform: scale(0.98);
}

.chart {
    height: calc(100% - 50px);
    padding: 10px;
}

/* 底部状态栏 */
.footer {
    height: 50px;
    background: linear-gradient(90deg,
        rgba(26, 31, 46, 0.95) 0%,
        rgba(42, 49, 66, 0.95) 50%,
        rgba(26, 31, 46, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    font-size: 12px;
    color: var(--text-muted);
    animation: fadeInUp 0.6s ease-out 0.5s both;
}

.footer-left,
.footer-center,
.footer-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.system-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 重复的 .connection-status 定义已删除，使用上面的完整定义 */

.signal-strength {
    display: flex;
    gap: 2px;
    align-items: end;
}

.signal-bar {
    width: 3px;
    background: var(--text-muted);
    border-radius: 1px;
}

.signal-bar:nth-child(1) { height: 6px; }
.signal-bar:nth-child(2) { height: 9px; }
.signal-bar:nth-child(3) { height: 12px; }
.signal-bar:nth-child(4) { height: 15px; }

.signal-bar.active {
    background: var(--success-color);
}

.copyright {
    color: var(--text-muted);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 212, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--accent-color), var(--primary-color));
}

/* 工具提示 */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-secondary);
    z-index: 1000;
    animation: tooltipFadeIn 0.2s ease-out;
}

[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(1px);
    border: 5px solid transparent;
    border-top-color: var(--border-color);
    z-index: 1000;
}

/* 动画关键帧定义 */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes logoSpin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loadingProgress {
    0% {
        width: 0%;
        opacity: 1;
    }
    50% {
        width: 70%;
        opacity: 1;
    }
    100% {
        width: 100%;
        opacity: 0.8;
    }
}

@keyframes dataFlow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes scanLine {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(calc(100% + 100vh - 380px));
    }
}

@keyframes neonGlow {
    0%, 100% {
        text-shadow:
            0 0 5px var(--primary-color),
            0 0 10px var(--primary-color),
            0 0 15px var(--primary-color);
    }
    50% {
        text-shadow:
            0 0 10px var(--primary-color),
            0 0 20px var(--primary-color),
            0 0 30px var(--primary-color),
            0 0 40px var(--primary-color);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes tooltipFadeIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 波纹效果 */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 212, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 高性能动画优化 */
.control-btn,
.toolbar-btn,
.metric-card,
.chart-container {
    will-change: transform;
}

.control-btn:hover,
.toolbar-btn:hover,
.metric-card:hover,
.chart-container:hover {
    will-change: auto;
}

/* 响应式设计 - 适应三栏布局 */
/* 1080p显示器优化 */
@media (max-width: 1920px) and (max-height: 1080px) {
    .main-content {
        grid-template-columns: 1fr 3fr 1fr;
        gap: 12px;
        padding: 12px;
        height: calc(100vh - 140px);
    }

    .status-section,
    .cooling-status-section {
        padding: 10px 12px 0px 12px;
    }

    .parameters-section,
    .cooling-parameters {
        padding: 0px 12px 10px 12px;
    }

    .panel-header {
        padding: 10px 12px;
    }

    .panel-header h3 {
        font-size: 16px;
    }

    .harmonic-chart-section {
        padding: 10px;
    }

    .topology-links-section {
        padding: 10px;
    }

    .right-panel {
        overflow: hidden; /* 防止滚动条 */
    }

    .status-section h4,
    .parameters-section h4,
    .cooling-parameters h4 {
        font-size: 13px;
        margin-bottom: 4px;
        padding: 6px 10px;
    }

    .alarm-monitor-section {
        height: 180px;
    }

    .status-grid,
    .status-grid-horizontal,
    .status-grid-five,
    .cooling-status-grid {
        gap: 4px;
        margin-bottom: 10px;
    }

    /* .status-grid-five {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
    } */

    .status-item,
    .cooling-status-item {
        padding: 6px 3px;
        min-height: 50px;
    }

    .status-indicator {
        width: 18px;
        height: 18px;
        font-size: 9px;
    }

    .status-item .status-label,
    .cooling-status-item .status-label {
        font-size: 8px;
        line-height: 1.0;
    }

    .parameter-grid,
    .parameter-grid-compact {
        gap: 5px;
    }

    .parameter-item,
    .cooling-param-card {
        padding: 6px;
        min-height: 50px;
    }

    .parameter-icon,
    .param-icon {
        width: 30px;
        height: 30px;
        font-size: 13px;
    }

    .parameter-label,
    .param-label {
        font-size: 10px;
    }

    .parameter-value,
    .param-value {
        font-size: 13px;
    }

    .harmonic-chart-section .chart-content {
        min-height: 160px;
    }

    .topology-link-btn {
        padding: 10px 12px;
        font-size: 13px;
    }

    .topology-link-btn i {
        font-size: 16px;
    }
}

@media (max-width: 1600px) {
    .main-content {
        grid-template-columns: 1fr 3fr 1fr;
    }
}

@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 1fr 3fr 1fr;
    }

    .header {
        padding: 0 20px;
    }

    .system-title {
        font-size: 18px;
    }

    .controls {
        gap: 10px;
    }

    .control-btn {
        padding: 10px 14px;
        font-size: 13px;
    }
}

@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 15px;
    }

    .left-panel,
    .right-panel {
        height: 250px;
        overflow-y: auto;
    }

    .header-left {
        gap: 20px;
    }

    .header-right {
        gap: 20px;
    }

    .alarm-monitor-section {
        height: 200px;
    }
}

@media (max-width: 1000px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }

    .parameter-grid {
        grid-template-columns: 1fr 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 8px;
    }

    .status-item {
        padding: 8px;
    }

    .status-indicator {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .status-item .status-label {
        font-size: 10px;
    }

    .control-buttons {
        gap: 6px;
    }

    .control-button {
        padding: 8px 6px;
        font-size: 10px;
    }

    .control-button i {
        font-size: 14px;
    }

    .header-center .controls {
        gap: 8px;
    }

    .control-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
        gap: 15px;
    }

    .header-left,
    .header-center,
    .header-right {
        width: 100%;
        justify-content: center;
    }

    .system-info-panel {
        flex-direction: column;
        align-items: center;
        min-width: auto;
        gap: 8px;
    }

    .time-display {
        font-size: 13px;
        padding: 6px 14px;
    }

    .connection-status {
        font-size: 11px;
        padding: 5px 10px;
    }

    .main-content {
        padding: 10px;
        gap: 10px;
    }

    .footer {
        flex-direction: column;
        height: auto;
        padding: 10px 20px;
        gap: 10px;
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== 新增样式：左侧面板 ===== */

/* 状态区域 */
.status-section {
    padding: 12px 15px 12px 15px;
    border-bottom: none;
    flex-shrink: 0;
}

/* 二级标题样式 - 与主标题明显区别 */
.status-section h4,
.parameters-section h4,
.cooling-parameters h4,
.harmonic-chart-section h4,
.cooling-status-section h4,
.topology-links-section .section-header h4 {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    background: linear-gradient(90deg, rgba(0, 212, 255, 0.12), rgba(0, 153, 204, 0.08), transparent);
    padding: 6px 10px;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

/* 二级标题的动态效果 */
.status-section h4::before,
.parameters-section h4::before,
.cooling-parameters h4::before,
.harmonic-chart-section h4::before,
.topology-links-section .section-header h4::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    animation: titleShimmer 3s infinite;
}

/* 二级标题图标样式 */
.status-section h4 i,
.parameters-section h4 i,
.cooling-parameters h4 i,
.harmonic-chart-section h4 i,
.cooling-status-section h4 i,
.topology-links-section .section-header h4 i {
    color: var(--primary-color);
    font-size: 14px;
    filter: drop-shadow(0 0 4px rgba(0, 212, 255, 0.5));
}

/* 添加标题闪烁动画 */
@keyframes titleShimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: -100%; }
}

/* 状态网格布局 - 5种状态布局 */
.status-grid-five {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 6px;
    margin-bottom: 12px;
}

/* 在较小屏幕上使用2行布局 */
@media (max-width: 1400px) {
    .status-grid-five {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 5px;
    }
}

/* 状态网格布局 - 3项水平布局 */
.status-grid-horizontal {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

/* 保持原有的状态网格布局兼容性 */
.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 5px;
    margin-bottom: 8px;
}

/* 状态项样式 - 合并电气系统和水冷系统状态样式 */
.status-item,
.cooling-status-item {
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 6px;
    padding: 8px 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 60px;
    justify-content: center;
}

.status-item::before,
.cooling-status-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: transparent;
    transition: all 0.3s ease;
}

.status-item:hover,
.cooling-status-item:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: translateY(-2px);
}

.status-item.active,
.cooling-status-item.active {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.15) 0%,
        rgba(0, 153, 204, 0.15) 100%);
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.status-item.active::before,
.cooling-status-item.active::before {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    position: relative;
}

/* 不同状态的颜色 */
.status-indicator.ready {
    background: rgba(128, 128, 128, 0.2);
    color: var(--text-secondary);
    border: 2px solid var(--text-secondary);
}

.status-indicator.fault {
    background: rgba(255, 68, 68, 0.2);
    color: var(--error-color);
    border: 2px solid var(--error-color);
}

.status-indicator.charging {
    background: rgba(255, 170, 0, 0.2);
    color: var(--warning-color);
    border: 2px solid var(--warning-color);
}

.status-indicator.waiting {
    background: rgba(0, 212, 255, 0.2);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.status-indicator.running {
    background: rgba(0, 255, 136, 0.2);
    color: var(--success-color);
    border: 2px solid var(--success-color);
}

.status-item.active .status-indicator.running i {
    animation: pulse 2s ease-in-out infinite;
}

/* 状态标签样式 - 合并电气系统和水冷系统 */
.status-item .status-label,
.cooling-status-item .status-label {
    font-size: 9px;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 500;
    line-height: 1.1;
    word-break: break-all;
    max-width: 100%;
}

.status-item.active .status-label,
.cooling-status-item.active .status-label {
    color: var(--text-primary);
    font-weight: 600;
}

/* 控制按钮区域 */
.control-buttons {
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

.control-button {
    flex: 1;
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(0, 153, 204, 0.1) 100%);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    position: relative;
    overflow: hidden;
}

.control-button i {
    font-size: 16px;
    margin-bottom: 2px;
}

.control-button:hover {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.2) 0%,
        rgba(0, 153, 204, 0.2) 100%);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.control-button.start:hover {
    border-color: var(--success-color);
    color: var(--success-color);
}

.control-button.stop:hover {
    border-color: var(--error-color);
    color: var(--error-color);
}

.control-button.reset:hover {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.control-button:active {
    transform: scale(0.98);
}

/* 参数区域 */
.parameters-section {
    padding: 0px 15px 12px 15px;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

/* 参数区域样式已在上面的二级标题样式中统一定义 */

.parameter-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

/* 关键参数网格布局 - 支持多列显示 */
.parameters-section .parameter-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

/* 紧凑型参数网格布局 - 2-3列网格，紧凑排列 */
.parameter-grid-compact {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    margin-top: 0px;
}

/* 在较大屏幕上使用3列布局 */
@media (min-width: 1400px) {
    .parameter-grid-compact {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }
}

/* 参数项样式 - 合并参数项和水冷参数卡样式 */
.parameter-item,
.cooling-param-card {
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 6px;
    padding: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 45px;
}

.parameter-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateX(5px);
}

.cooling-param-card:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--primary-color);
    transform: scale(1.02);
}

/* 参数样式 - 合并 parameter 和 param 类 */
.parameter-icon,
.param-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-primary);
    font-size: 14px;
    flex-shrink: 0;
}

.parameter-content,
.param-content {
    flex: 1;
}

.parameter-label,
.param-label {
    font-size: 11px;
    color: var(--text-secondary);
    margin-bottom: 1px;
    line-height: 1.2;
}

.parameter-value,
.param-value {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-primary);
    font-family: var(--font-mono);
    line-height: 1.2;
}

.parameter-item > i {
    color: var(--text-muted);
    font-size: 12px;
}

/* 拓扑图区域 - 合并电气系统和水冷系统拓扑样式 */
.topology-section,
.cooling-topology-section {
    padding: 16px;
    flex: 1;
    min-height: 0; /* 允许flex收缩 */
}

.topology-section h4,
.cooling-topology-section h4 {
    font-size: 15px;
    color: var(--primary-color);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.topology-section h4::before,
.cooling-topology-section h4::before {
    content: '';
    width: 3px;
    height: 16px;
    background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.topology-container {
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 6px;
    padding: 12px;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.topology-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

/* 重复的 .topology-image 定义已删除，使用上面包含 object-fit: contain 的完整定义 */

.topology-placeholder {
    text-align: center;
    color: var(--text-secondary);
}

.topology-placeholder i {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: block;
}

.topology-placeholder p {
    font-size: 14px;
}

/* ===== 新增样式：实时报警监控区域 ===== */

.alarm-monitor-section {
    background: var(--bg-card);
    border-top: 1px solid var(--border-color);
    height: 240px;
    display: flex;
    flex-direction: column;
}

.alarm-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.1) 0%,
        rgba(0, 153, 204, 0.05) 100%);
}

.alarm-header h4 {
    font-size: 15px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 6px;
}

.alarm-filters {
    display: flex;
    gap: 8px;
}

.filter-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--bg-primary);
}

.alarm-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 20px;
}




/* ===== 新增样式：系统标题和菜单 ===== */

.system-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
}

.main-title {
    font-size: 20px;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.main-title:hover {
    color: var(--accent-color);
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

.menu-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-secondary);
}

.menu-btn:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary);
}

.main-menu-dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-10px);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-primary);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.main-menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.menu-item {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: rgba(0, 212, 255, 0.1);
    color: var(--primary-color);
}

.menu-item i {
    width: 16px;
    text-align: center;
}

/* ===== 新增样式：水冷系统 ===== */

/* 移除水冷系统区域的多余边距，现在由system-status-area和cooling-system-area控制布局 */

.cooling-status-section {
    padding: 12px 15px 12px 15px;
    border-bottom: none;
    flex-shrink: 0;
}

.cooling-status-card {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.08) 0%,
        rgba(0, 153, 204, 0.08) 100%);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.cooling-parameters {
    padding: 0px 15px 12px 15px;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

/* 水冷参数区域样式已在上面的二级标题样式中统一定义 */

/* 水冷系统参数网格布局 */
.cooling-parameters .parameter-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5px;
}

/* 水冷系统状态网格 - 与电气系统状态样式完全一致 */
.cooling-status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 6px;
    margin-bottom: 12px;
}

/* 重复的水冷系统状态样式已删除，使用上面合并的状态样式 */

/* 重复的水冷参数卡样式已删除，使用上面合并的参数项样式 */

/* 重复的 param 样式定义已删除，使用上面合并的 parameter/param 样式 */

/* 重复的水冷拓扑样式已删除，使用上面合并的拓扑样式 */

/* ===== 新增样式：SVG电流曲线 ===== */

.svg-current-section {
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
    flex: none;
    min-height: auto;
    display: flex;
    flex-direction: column;
}

.svg-current-section:last-of-type {
    border-bottom: none;
}

.current-curve-container {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.12) 0%,
        rgba(0, 153, 204, 0.08) 50%,
        rgba(102, 224, 255, 0.06) 100%);
    border: 2px solid rgba(0, 212, 255, 0.4);
    border-radius: 8px;
    padding: 8px;
    margin-top: 8px;
    position: relative;
    box-shadow:
        0 4px 15px rgba(0, 212, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.current-curve-container:hover {
    border-color: var(--accent-color);
    box-shadow:
        0 6px 20px rgba(0, 212, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.current-curve-header {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 8px;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

.current-curve-display {
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    padding: 4px;
}

.current-curve-display canvas {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

@keyframes currentPulse {
    0%, 100% {
        box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
    }
}

/* ===== 新增样式：相关链接区域 ===== */

.topology-links-section {
    padding: 12px;
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.05) 0%,
        rgba(0, 153, 204, 0.03) 100%);
}

/* 重复的拓扑链接标题样式已删除，使用上面统一的二级标题样式 */

.topology-links-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 8px;
    height: auto;
}

.topology-link-btn {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.15) 0%,
        rgba(0, 153, 204, 0.12) 50%,
        rgba(102, 224, 255, 0.08) 100%);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 10px;
    padding: 12px 16px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.4s ease;
    text-decoration: none;
    box-shadow:
        0 4px 12px rgba(0, 212, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.topology-link-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 212, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.topology-link-btn:hover::before {
    left: 100%;
}

.topology-link-btn:hover {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.25) 0%,
        rgba(0, 153, 204, 0.20) 50%,
        rgba(102, 224, 255, 0.15) 100%);
    border-color: var(--primary-color);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 25px rgba(0, 212, 255, 0.25),
        0 0 20px rgba(0, 212, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.topology-link-btn i {
    font-size: 18px;
    color: var(--primary-color);
    width: 20px; /* 统一图标宽度，确保整齐排列 */
    text-align: center;
    flex-shrink: 0;
    filter: drop-shadow(0 0 6px rgba(0, 212, 255, 0.4));
    transition: all 0.3s ease;
}

.topology-link-btn:hover i {
    color: var(--accent-color);
    transform: scale(1.1);
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.6));
}

.topology-link-btn span {
    font-size: 14px;
    font-weight: 600;
    flex: 1;
    letter-spacing: 0.3px;
}

/* ===== 更新样式：报警监控日期时间分列 ===== */

.alarm-item {
    display: grid;
    grid-template-columns: 50px 100px 80px 80px 1fr;
    gap: 8px;
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    align-items: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.alarm-item:hover {
    background: rgba(0, 212, 255, 0.05);
}

.alarm-serial {
    text-align: right;
    font-weight: bold;
    font-family: var(--font-mono);
    padding-right: 8px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.alarm-date {
    font-family: var(--font-mono);
    font-size: 11px;
}

.alarm-time {
    font-family: var(--font-mono);
    font-size: 11px;
}

.alarm-device {
    font-weight: 500;
    color: var(--primary-color);
}

.alarm-message {
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 不同类型报警的颜色 */
.alarm-item.alarm .alarm-serial,
.alarm-item.alarm .alarm-date,
.alarm-item.alarm .alarm-time {
    color: var(--warning-color);
}

.alarm-item.fault .alarm-serial,
.alarm-item.fault .alarm-date,
.alarm-item.fault .alarm-time {
    color: var(--error-color);
}

.alarm-item.recovery .alarm-serial,
.alarm-item.recovery .alarm-date,
.alarm-item.recovery .alarm-time {
    color: var(--text-primary);
}

/* ==================== I/O状态弹窗样式 ==================== */

/* 弹窗遮罩层 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

/* 弹窗容器 */
.modal-container {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--shadow-primary);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

/* I/O状态弹窗特定样式 */
.io-status-modal {
    width: var(--modal-width);
    height: var(--modal-height);
}

/* 弹窗头部 */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(90deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
    border-bottom: 2px solid var(--border-color);
}

.modal-header h2 {
    color: var(--primary-color);
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-header h2 i {
    font-size: 28px;
}

.modal-close-btn {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close-btn:hover {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
    transform: scale(1.1);
}

/* 弹窗内容 */
.modal-content {
    padding: 30px;
    height: calc(100% - 84px);
    overflow-y: auto;
}

/* I/O状态网格布局 */
.io-status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    height: 100%;
}

/* I/O区域 */
.io-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.io-section-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.io-section-header h3 {
    color: var(--primary-color);
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.io-section-header h3 i {
    font-size: 22px;
}

/* I/O列布局 */
.io-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: calc(100% - 70px);
}

.io-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* I/O项目 */
.io-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 36px;
}

.io-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.4);
    transform: translateX(2px);
}

.io-label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    flex: 1;
    text-align: left;
}

/* I/O指示器 */
.io-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

/* 输入指示器（圆形） */
.input-section .io-indicator {
    border-radius: 50%;
}

/* 输出指示器（方形） */
.output-section .io-indicator {
    border-radius: 4px;
}

/* 激活状态 */
.io-indicator.active {
    background: var(--success-color);
    color: white;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    animation: pulse-green 2s infinite;
}

/* 非激活状态 */
.io-indicator.inactive {
    background: var(--text-muted);
    color: var(--bg-primary);
    opacity: 0.6;
}

/* 脉冲动画 */
@keyframes pulse-green {
    0%, 100% {
        box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
    }
}

/* 输入输出区域差异化样式 */
.input-section {
    border-left: 4px solid var(--primary-color);
}

.output-section {
    border-left: 4px solid var(--warning-color);
}

.input-section .io-section-header h3 {
    color: var(--primary-color);
}

.output-section .io-section-header h3 {
    color: var(--warning-color);
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .io-status-modal {
        width: 95vw;
        height: 85vh;
    }
}

@media (max-width: 1024px) {
    .io-status-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .io-columns {
        grid-template-columns: 1fr;
    }

    .io-status-modal {
        width: 95vw;
        height: 90vh;
    }
}

/* ==================== 拓扑图弹窗样式 ==================== */

/* 拓扑图弹窗特定样式 */
.topology-modal {
    width: var(--modal-width);
    height: var(--modal-height);
}

/* 拓扑图容器 */
.topology-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
}

/* 拓扑图iframe容器 */
.topology-iframe-container {
    width: 100%;
    height: 100%;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
}

/* 拓扑图图片 */
.topology-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    transition: transform 0.3s ease;
    cursor: zoom-in;
}

.topology-image:hover {
    transform: scale(1.02);
}

/* 拓扑图iframe */
.topology-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
    background: #fff;
}

/* 拓扑图弹窗响应式设计 */
@media (max-width: 1200px) {
    .topology-modal {
        width: 90vw;
        height: 80vh;
    }
}

@media (max-width: 768px) {
    .topology-modal {
        width: 95vw;
        height: 85vh;
    }
}

/* ==================== 单元状态弹窗样式 ==================== */

/* 单元状态弹窗特定样式 */
.unit-status-modal {
    width: var(--modal-width);
    height: var(--modal-height);
}

/* 单元状态布局 */
.unit-status-layout {
    display: grid;
    grid-template-columns: 300px 1fr 280px;
    gap: 20px;
    height: 100%;
}

/* 左侧单元列表区域 */
.unit-list-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--primary-color);
}

.unit-list-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.unit-list-header h3 {
    color: var(--primary-color);
    font-size: 18px;
    font-weight: 600;
}

.unit-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: calc(100% - 70px);
    overflow-y: auto;
}

/* 单元项目 */
.unit-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 15px;
}

.unit-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.4);
    transform: translateX(3px);
}

.unit-item.selected {
    background: rgba(0, 212, 255, 0.2);
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.unit-item.active {
    border-left: 4px solid var(--success-color);
}

.unit-id {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 16px;
    min-width: 40px;
}

.unit-voltage {
    color: var(--text-secondary);
    font-family: var(--font-mono);
    font-size: 14px;
    min-width: 40px;
}

/* 单元指示器 */
.unit-indicators {
    display: flex;
    gap: 4px;
    margin-left: auto;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.indicator-dot.active {
    background: var(--success-color);
    box-shadow: 0 0 6px rgba(0, 255, 136, 0.6);
}

.indicator-dot.inactive {
    background: var(--text-muted);
    opacity: 0.5;
}

/* 中间详细状态区域 */
.unit-detail-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--warning-color);
}

.unit-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.unit-detail-header h3 {
    color: var(--warning-color);
    font-size: 18px;
    font-weight: 600;
}

.close-detail-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-detail-btn:hover {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

/* 状态网格 */
.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    max-height: calc(100% - 70px);
    overflow-y: auto;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.status-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.4);
}

.status-label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    flex: 1;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background: var(--success-color);
    color: white;
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
    animation: pulse-green 2s infinite;
}

.status-indicator.inactive {
    background: var(--text-muted);
    color: var(--bg-primary);
    opacity: 0.6;
}

/* 右侧状态统计区域 */
.unit-stats-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stats-header {
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 15px;
}

.stats-header h3 {
    color: var(--accent-color);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
}

/* 电压显示 */
.voltage-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.voltage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(102, 224, 255, 0.05);
    border: 1px solid rgba(102, 224, 255, 0.2);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.voltage-item:hover {
    background: rgba(102, 224, 255, 0.1);
    border-color: rgba(102, 224, 255, 0.4);
}

.voltage-label {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
}

.voltage-value {
    color: var(--accent-color);
    font-family: var(--font-mono);
    font-size: 13px;
    font-weight: 600;
}

/* 单元网格显示 */
.unit-grid-display {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.grid-section h4 {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
}

.unit-mini-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.mini-unit {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 6px;
    padding: 12px 8px;
    text-align: center;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1.2;
}

.mini-unit:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.05);
}

/* 单元状态弹窗响应式设计 */
@media (max-width: 1600px) {
    .unit-status-modal {
        width: 95vw;
        height: 85vh;
    }

    .unit-status-layout {
        grid-template-columns: 280px 1fr 250px;
    }
}

@media (max-width: 1200px) {
    .unit-status-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 15px;
    }

    .unit-list {
        max-height: 200px;
    }

    .status-grid {
        grid-template-columns: 1fr;
        max-height: 400px;
    }

    .unit-mini-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .unit-status-modal {
        width: 95vw;
        height: 90vh;
    }

    .unit-status-layout {
        gap: 10px;
    }

    .modal-content {
        padding: 15px;
    }
}

/* ==================== 模块弹窗样式 ==================== */

/* 模块弹窗特定样式 */
.module-modal {
    width: auto;
    height: auto;
    padding: 20px;
}

/* 模块iframe容器 */
.module-iframe-container {
    width: 1366px;
    height: 768px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
}

/* 模块iframe */
.module-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 10px;
    background: #fff;
}

/* 模块弹窗响应式设计 */
@media (max-width: 1400px) {
    .module-modal {
        width: 95vw;
        height: 80vh;
    }
}

@media (max-width: 768px) {
    .module-modal {
        width: 95vw;
        height: 85vh;
    }
}
